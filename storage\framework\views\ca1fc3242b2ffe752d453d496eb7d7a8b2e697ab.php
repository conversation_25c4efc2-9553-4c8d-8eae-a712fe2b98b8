<meta charset="UTF-8" />
<meta name="robots" content="max-image-preview:large" />
<meta name="generator" content="All in One SEO (AIOSEO) 4.4.3" />
<meta property="og:locale" content="en_US" />
<meta property="og:site_name" content="Codibu - One step website solution" />
<meta property="og:type" content="article" />
<meta property="og:image" content="<?php echo e(asset('assets/images/logo_1000x1000-e1687794219439.png'), false); ?>" />
<meta property="og:image:secure_url" content="<?php echo e(asset('assets/images/logo_1000x1000-e1687794219439.png'), false); ?>" />
<meta property="og:image:width" content="112" />
<meta property="article:publisher" content="https://www.facebook.com/codibucom" />
<meta property="og:image:height" content="112" />
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:image" content="<?php echo e(asset('assets/images/logo_1000x1000-e1687794219439.png'), false); ?>" />
<meta name="format-detection" content="telephone=no">
<meta name="generator" content="WordPress 6.2.2" />
<meta name="generator" content="WPML ver:4.6.4 stt:1,29;" />
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
<meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
<meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: dark)">
<meta name="generator" content="Elementor 3.15.3; features: e_dom_optimization, e_optimized_assets_loading, e_optimized_css_loading, additional_custom_breakpoints; settings: css_print_method-external, google_font-enabled, font_display-swap">
<meta name="generator" content="Powered by Slider Revolution 6.6.15 - responsive, Mobile-Friendly Slider Plugin for WordPress with comfortable drag and drop interface." />
<link rel="shortcut icon" href="<?php echo e(asset('assets/images/codibu-favicon.png'), false); ?>" type="image/x-icon" />
<link rel="apple-touch-icon" href="<?php echo e(asset('assets/images/apple-touch-icon-1.png'), false); ?>" />
<link rel='dns-prefetch' href='//fonts.googleapis.com' />
<link rel="alternate" type="application/rss+xml" title="Codibu &raquo; Feed" href="https://p.codibu.com/feed/" />
<link rel="wlwmanifest" type="application/wlwmanifest+xml" href="https://p.codibu.com/wp-includes/wlwmanifest.xml" />
<link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://p.codibu.com/xmlrpc.php?rsd" />
<link rel="https://api.w.org/" href="https://p.codibu.com/wp-json/" />
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
<!-- <link rel='stylesheet' id='google-fonts-1-css' href='https://fonts.googleapis.com/css?family=Roboto%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic&#038;display=swap&#038;ver=6.2.2' type='text/css' media='all' /> -->
<link rel='stylesheet' id='google-fonts-1-css' href='<?php echo e(asset("assets/opt/fontcss2.css"), false); ?>' type='text/css' media='all' />
<!-- <link rel='stylesheet' id='mfn-font-button-css' href='https://fonts.googleapis.com/css?family=Open+Sans%3A400%2C600&#038;display=swap&#038;ver=6.2.2' type='text/css' media='all' /> -->
<link rel='stylesheet' id='mfn-font-button-css' href='<?php echo e(asset("assets/opt/fontcss3.css"), false); ?>' type='text/css' media='all' />
<!-- <link rel='stylesheet' id='mfn-fonts-css' href='https://fonts.googleapis.com/css?family=Open+Sans%3A1%2C500%2C600%2C700&#038;display=swap&#038;ver=6.2.2' type='text/css' media='all' /> -->
<link rel='stylesheet' id='mfn-fonts-css' href='<?php echo e(asset("assets/opt/fontcss4.css"), false); ?>' type='text/css' media='all' />
<link rel="alternate" type="application/rss+xml" title="Codibu &raquo; Comments Feed" href="https://p.codibu.com/comments/feed/" />
<link rel='stylesheet' href='<?php echo e(asset("assets/css/dist-block-library-style.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/classic-themes.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/sitepress-multilingual-cms-dist-css-blocks-styles.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/contact-form-7-includes-css-styles.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/header-footer-elementor-assets-css-header-footer-elementor.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-assets-lib-eicons-css-elementor-icons.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-assets-css-frontend-lite.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-assets-lib-swiper-v8-css-swiper.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-css-post-6.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-pro-assets-css-frontend-lite.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-css-global.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/header-footer-elementor-inc-widgets-css-frontend.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/wpml-cms-nav-res-css-cms-navigation-base.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/wpml-cms-nav-res-css-cms-navigation.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-css-be.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-assets-animations-animations.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-fonts-fontawesome-fontawesome.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-assets-jplayer-css-jplayer.blue.monday.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementskit-lite-modules-elementskit-icon-pack-assets-css-ekiticons.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-css-static.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementskit-lite-widgets-init-assets-css-widget-styles.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementskit-lite-widgets-init-assets-css-responsive.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-child-style.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/revslider-public-assets-css-rs6.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/betheme-functions-plugins-elementor-assets-elementor.css"), false); ?>'>
<link rel='stylesheet' media="(max-width: 1025px)" href='<?php echo e(asset("assets/css/betheme-css-responsive.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/sitepress-multilingual-cms-templates-language-switchers-legacy-list-horizontal-style.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/export-wp-page-to-static-html-pro-premium-public-css-export-wp-page-to-static-html-public.css"), false); ?>'>
<link rel="stylesheet" href="<?php echo e(asset('css/custom.css'), false); ?>">
<link rel="stylesheet" href="<?php echo e(asset('css/new-custom.css'), false); ?>">
<script type='text/javascript' src='<?php echo e(asset("assets/js/jquery.min.js"), false); ?>' id='jquery-core-js'></script>
<!-- dev & design & hosting & seo -->
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-assets-lib-animations-animations.min.css"), false); ?>'>
<!-- contact & hosting & pricing & seo -->
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-assets-lib-font-awesome-css-fontawesome.min.css"), false); ?>'>
<link rel='stylesheet' href='<?php echo e(asset("assets/css/elementor-assets-lib-font-awesome-css-solid.min.css"), false); ?>'>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-5NN6VL8');</script>
    <!-- End Google Tag Manager -->
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5NN6VL8');</script>
<!-- End Google Tag Manager -->
<!-- Meta Pixel Code --> <script> !function(f,b,e,v,n,t,s) {if(f.fbq)return;n=f.fbq=function(){n.callMethod? n.callMethod.apply(n,arguments):n.queue.push(arguments)}; if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0'; n.queue=[];t=b.createElement(e);t.async=!0; t.src=v;s=b.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s)}(window, document,'script', ''); fbq('init', '671169254592324'); fbq('track', 'PageView'); </script> <noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=671169254592324&ev=PageView&noscript=1" /></noscript> <!-- End Meta Pixel Code -->
<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=AW-10951858704"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-10951858704'); </script>
<?php /**PATH C:\laragon\www\codibu\resources\views/frontend/layout/header_link.blade.php ENDPATH**/ ?>